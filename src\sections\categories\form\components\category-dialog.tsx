import React from 'react';
import { Dialog, DialogContent, IconButton, Typography, Box, Stack, DialogActions } from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Form } from 'src/components/hook-form/form-provider';
import { Category } from 'src/services/api/use-categories-api';
import CategoryFormFields from './category-form-fields';
import { CategoryFormValues, createCategorySchema } from '../category-schema';

interface CategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: CategoryFormValues) => void;
  defaultValues?: Partial<Category>;
  loading?: boolean;
}

export default function CategoryDialog({
  open,
  onClose,
  onSubmit,
  defaultValues,
  loading = false,
}: CategoryDialogProps) {
  const { t } = useTranslation();

  // Form methods
  const methods = useForm<CategoryFormValues>({
    mode: 'onChange',
    resolver: zodResolver(createCategorySchema(t)),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      colorType: 'primary',
      customColor: '#FF5733',
      ...defaultValues,
    },
  });

  const { handleSubmit, formState: { isSubmitting }, reset } = methods;

  // Reset form when dialog opens/closes or defaultValues change
  React.useEffect(() => {
    if (open && defaultValues) {
      reset({
        name: '',
        description: '',
        icon: '',
        colorType: 'primary',
        customColor: '#FF5733',
        ...defaultValues,
      });
    }
  }, [open, defaultValues, reset]);
  const renderHead = (
    <Stack spacing={1.5} sx={{ mb: 5 }}>
      <Typography variant="h3" textAlign="center">
        {defaultValues ? 'Edit Category' : 'Add New Category'}
      </Typography>

      <Typography variant="body2" sx={{ color: 'text.secondary' }} textAlign="center">
        Please type the category name with description below
      </Typography>
    </Stack>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: 712,
          borderRadius: 2,
          px: 4,
        },
      }}
    >
      <Box sx={{ position: 'relative', p: 3 }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
        >
          <Iconify icon="eva:close-fill" width={24} height={24} />
        </IconButton>

        {renderHead}

        <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
          <DialogContent sx={{ p: 0 }}>
            <CategoryFormFields />
          </DialogContent>

          <DialogActions sx={{ p: 3, pt: 0 }}>
            <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
              <AppButton
                variant="outlined"
                color="inherit"
                label="Cancel"
                onClick={onClose}
                sx={{
                  height: '48px',
                  px: 4,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                }}
              />
              <AppButton
                type="submit"
                variant="contained"
                color="primary"
                isLoading={isSubmitting || loading}
                label={defaultValues ? 'Update' : 'Create'}
                sx={{
                  height: '48px',
                  px: 4,
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '1rem',
                  fontWeight: 500,
                }}
              />
            </Stack>
          </DialogActions>
        </Form>
      </Box>
    </Dialog>
  );
}
