
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import CategoryForm from 'src/sections/categories/form/category-form';

const CreateCategoryPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('categories.createNew')}`}</title>
      </Helmet>
      <CategoryForm />
    </>
  );
};

export default CreateCategoryPage;
