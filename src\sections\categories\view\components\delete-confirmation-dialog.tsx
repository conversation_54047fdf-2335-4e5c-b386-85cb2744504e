import React from 'react';
import { Typography, Box, Stack } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { ConfirmDialog } from 'src/components/custom-dialog';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  categoryName: string;
  isDeleting?: boolean;
}

export default function DeleteConfirmationDialog({
  open,
  onClose,
  onConfirm,
  categoryName,
  isDeleting = false,
}: DeleteConfirmationDialogProps) {
  return (
    <ConfirmDialog
      open={open}
      onClose={onClose}
      close={onClose}
      title={
        <Typography variant="h5" textAlign="start">
          Delete Category?
        </Typography>
      }
      content={
        <Typography variant="body1" textAlign="start">
          Are you sure you want to delete <strong>{categoryName}</strong>?
        </Typography>
      }
     
      action={
        <Stack direction="row" justifyContent="end" spacing={2} sx={{ width: '100%' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={onClose}
            sx={{
              width: '20%',
              height: '31.9970703125px',
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Delete"
            onClick={onConfirm}
            isLoading={isDeleting}
            sx={{
              width: '20%',
              height: '31.9970703125px',
              borderRadius: 1,
              textTransform: 'none',
              fontSize: '1rem',
              fontWeight: 500,
            }}
          />
        </Stack>
      }
    />
  );
}
