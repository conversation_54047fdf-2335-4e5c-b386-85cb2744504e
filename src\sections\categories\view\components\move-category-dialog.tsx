import { useState, useMemo, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Stack,
  IconButton,
  TextField,
  MenuItem,
  ListItemIcon,
  ListItemText,
  InputAdornment,
  ListSubheader,
  CircularProgress,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { Category, useCategoriesApi } from 'src/services/api/use-categories-api';

interface MoveCategoryDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (newCategoryId: number) => void;
  sourceCategory: Category | null;
  isMoving?: boolean;
}

export default function MoveCategoryDialog({
  open,
  onClose,
  onConfirm,
  sourceCategory,
  isMoving = false,
}: MoveCategoryDialogProps) {
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const searchFieldRef = useRef<HTMLInputElement>(null);

  // Get categories API hook
  const { useGetCategories } = useCategoriesApi();

  // Focus search field when dialog opens
  useEffect(() => {
    if (open && searchFieldRef.current) {
      // Small delay to ensure the dialog is fully rendered
      const timer = setTimeout(() => {
        searchFieldRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [open]);

  // Fetch all categories once (without search filter for better UX)
  const { data: categoriesResponse, isLoading: apiLoading } = useGetCategories({
    take: 100, // Get more categories for selection
    skip: 0,
  });
  const allCategories = categoriesResponse?.categories || [];

  // Filter out the source category and apply search filter
  const filteredCategories = useMemo(() => {
    let categories = allCategories.filter(
      (category: Category) => sourceCategory && category.id !== sourceCategory.id
    );

    // Apply client-side search filter
    if (searchQuery.trim()) {
      categories = categories.filter((category: Category) =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase().trim())
      );
    }

    return categories;
  }, [allCategories, sourceCategory, searchQuery]);

  const handleConfirm = () => {
    if (selectedCategoryId) {
      onConfirm(selectedCategoryId);
    }
  };

  const handleClose = () => {
    setSelectedCategoryId(null);
    setSearchQuery('');
    onClose();
  };

  // Don't render if no source category or no templates to move
  if (!sourceCategory || (sourceCategory.templatesCount || 0) === 0) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: 500,
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row">
            <IconButton onClick={handleClose} size="large">
              <Iconify icon="eva:swap-fill" width="45px" color="green" />
            </IconButton>
            <Typography variant="h6" sx={{ mt: 2.5 }}>
              Move Agents to Another Category
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This category contains {sourceCategory.templatesCount || 0} agents.
              </Typography>
            </Typography>{' '}
          </Stack>
          <IconButton onClick={handleClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Move {sourceCategory.templatesCount || 0} agents to
            </Typography>

            {filteredCategories.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body2" color="text.secondary">
                  No other categories available to move content to.
                </Typography>
              </Box>
            ) : (
              <TextField
                select
                fullWidth
                value={selectedCategoryId || ''}
                onChange={(e) => setSelectedCategoryId(Number(e.target.value) || null)}
                placeholder="Select Destination Category"
                SelectProps={{
                  displayEmpty: true,
                  autoFocus: false,
                  onOpen: () => {
                    // Focus the search field when the menu opens
                    setTimeout(() => {
                      if (searchFieldRef.current) {
                        searchFieldRef.current.focus();
                      }
                    }, 50);
                  },
                  onKeyDown: (e) => {
                    // Prevent the Select from closing on key presses that should be handled by the search field
                    if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
                      e.stopPropagation();
                      // Focus the search field and let it handle the key press
                      if (searchFieldRef.current) {
                        searchFieldRef.current.focus();
                        // Simulate the key press on the search field
                        const event = new KeyboardEvent('keydown', {
                          key: e.key,
                          bubbles: true,
                          cancelable: true,
                        });
                        searchFieldRef.current.dispatchEvent(event);
                      }
                    }
                  },
                  renderValue: (value) => {
                    if (!value) {
                      return (
                        <Typography variant="body2" color="text.secondary">
                          Select Destination Category
                        </Typography>
                      );
                    }
                    const category = filteredCategories.find((cat: Category) => cat.id === value);
                    if (!category) return '';
                    return (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: category.theme || 'primary.main',
                            color: 'white',
                          }}
                        >
                          <Iconify
                            icon={category.icon || 'eva:folder-fill'}
                            width={12}
                            height={12}
                          />
                        </Box>
                        <Typography variant="body2">{category.name}</Typography>
                      </Box>
                    );
                  },
                  MenuProps: {
                    disableAutoFocusItem: true,
                    onKeyDown: (e) => {
                      // Prevent menu from closing on key presses
                      if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete') {
                        e.stopPropagation();
                      }
                    },
                    PaperProps: {
                      sx: {
                        maxHeight: 400,
                        '& .MuiMenuItem-root': {
                          px: 2,
                          py: 1,
                        },
                      },
                    },
                  },
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                }}
              >
                {/* Search field as first menu item */}
                <ListSubheader sx={{ p: 0, bgcolor: 'background.paper', position: 'sticky', top: 0, zIndex: 1 }}>
                  <TextField
                    inputRef={searchFieldRef}
                    placeholder="Search categories..."
                    fullWidth
                    size="small"
                    value={searchQuery}
                    onChange={(e) => {
                      e.stopPropagation();
                      setSearchQuery(e.target.value);
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      // Ensure the search field gets focus
                      if (searchFieldRef.current) {
                        searchFieldRef.current.focus();
                      }
                    }}
                    onMouseDown={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                    onKeyDown={(e) => {
                      e.stopPropagation();
                      // Allow normal text input and navigation within the search field
                      if (e.key === 'ArrowDown' && filteredCategories.length > 0) {
                        // Allow arrow down to move to first category option
                        e.preventDefault();
                        const firstMenuItem = document.querySelector('[role="menuitem"]:not([aria-disabled="true"])');
                        if (firstMenuItem instanceof HTMLElement) {
                          firstMenuItem.focus();
                        }
                      } else if (e.key === 'Escape') {
                        // Allow escape to close the menu
                        e.preventDefault();
                        onClose();
                      }
                      // For all other keys (including Enter), let the search field handle them
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                        </InputAdornment>
                      ),
                      ...(searchQuery && {
                        endAdornment: (
                          <InputAdornment position="end">
                            <Box
                              component="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSearchQuery('');
                                // Refocus the search field after clearing
                                if (searchFieldRef.current) {
                                  searchFieldRef.current.focus();
                                }
                              }}
                              onMouseDown={(e) => e.stopPropagation()}
                              sx={{
                                border: 'none',
                                background: 'none',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                p: 0,
                              }}
                            >
                              <Iconify icon="eva:close-fill" sx={{ color: 'text.disabled' }} />
                            </Box>
                          </InputAdornment>
                        ),
                      }),
                      sx: {
                        p: 1,
                        borderRadius: 1,
                      },
                    }}
                    sx={{ m: 1, width: 'calc(100% - 16px)' }}
                  />
                </ListSubheader>

                {/* Loading indicator */}
                {apiLoading && (
                  <MenuItem disabled>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%', justifyContent: 'center' }}>
                      <CircularProgress size={16} />
                      <Typography variant="body2" color="text.secondary">
                        Loading categories...
                      </Typography>
                    </Box>
                  </MenuItem>
                )}

                {/* No results message */}
                {!apiLoading && filteredCategories.length === 0 && searchQuery && (
                  <MenuItem disabled>
                    <Typography variant="body2" color="text.secondary">
                      No categories found matching &ldquo;{searchQuery}&rdquo;
                    </Typography>
                  </MenuItem>
                )}

                {/* Category options */}
                {!apiLoading && filteredCategories.map((category: Category) => (
                  <MenuItem key={category.id} value={category.id}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          bgcolor: category.theme || 'primary.main',
                        }}
                      >
                        <Iconify
                          icon={category.icon || 'eva:folder-fill'}
                          width={16}
                          height={16}
                        />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={category.name}
                      secondary={`${category.templatesCount || 0} agents`}
                      primaryTypographyProps={{
                        variant: 'subtitle2',
                      }}
                      secondaryTypographyProps={{
                        variant: 'caption',
                        color: 'text.secondary',
                      }}
                    />
                  </MenuItem>
                ))}
              </TextField>
            )}
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1, backgroundColor: 'divider' }}>
        <Stack direction="row" spacing={2} sx={{ width: '100%', justifyContent: 'flex-end' }}>
          <AppButton
            variant="outlined"
            color="inherit"
            label="Cancel"
            onClick={handleClose}
            sx={{
              height: '32px',
              width: '25%',
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
          <AppButton
            variant="contained"
            color="error"
            label="Move & Delete"
            onClick={handleConfirm}
            disabled={!selectedCategoryId || filteredCategories.length === 0}
            isLoading={isMoving}
            sx={{
              whiteSpace: 'nowrap',
              height: '32px',
              width: '25%',
              px: 1,
              borderRadius: 1,
              textTransform: 'none',
            }}
          />
        </Stack>
      </DialogActions>
    </Dialog>
  );
}
