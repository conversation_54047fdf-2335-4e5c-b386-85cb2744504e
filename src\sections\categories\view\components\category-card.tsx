import {
  Box,
  Card,
  Chip,
  Icon<PERSON>utton,
  Menu,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import { useState } from 'react';
import { Iconify } from 'src/components/iconify';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

export interface CategoryCardProps {
  id: number | string;
  name: string;
  description: string;
  templatesCount: number;
  icon: string;
  color?: string;
  onDelete?: (id: number | string) => void;
  onEdit?: (id: number | string) => void;
  isDeleting?: boolean;
}

export default function CategoryCard({
  id,
  name,
  description,
  templatesCount,
  icon,
  color,
  onDelete,
  onEdit,
  isDeleting = false,
}: CategoryCardProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const open = Boolean(anchorEl);

  // Use the provided color or fall back to the theme's primary color
  const cardColor = color || theme.palette.primary.main;

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(id);
    }
    handleCloseMenu();
  };

  const handleOpenConfirmDialog = () => {
    setOpenConfirmDialog(true);
    handleCloseMenu();
  };

  const handleCloseConfirmDialog = () => {
    setOpenConfirmDialog(false);
  };

  const handleConfirmDelete = () => {
    if (onDelete) {
      onDelete(id);
    }
    setOpenConfirmDialog(false);
  };

  return (
    <Card
      sx={{
        p: 3,
        height: '291px',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        borderRadius: 2,
        boxShadow: theme.customShadows.card,
        bgcolor: theme.palette.background.default,
      }}
    >
      {/* Menu Button */}
      <IconButton
        size="small"
        onClick={handleOpenMenu}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          color: 'text.secondary',
        }}
      >
        <Iconify icon="eva:more-vertical-fill" width={20} height={20} />
      </IconButton>

      {/* Menu */}
      <Menu
        id="category-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseMenu}
        MenuListProps={{
          'aria-labelledby': 'category-menu-button',
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleEdit}>
          <Iconify icon="eva:edit-fill" width={20} height={20} sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleOpenConfirmDialog} sx={{ color: 'error.main' }}>
          <Iconify icon="eva:trash-2-outline" width={20} height={20} sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Icon */}
      <Box
        sx={{
          width: 80,
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: cardColor,
          mb: 2,
        }}
      >
        <Iconify icon={icon} width={80} height={80} />
      </Box>

      {/* Content */}
      <Stack spacing={1} sx={{ flexGrow: 1 }}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography variant="h4" noWrap>
            {name}
          </Typography>
          <Chip
            label={`${templatesCount} Agents`}
            size="small"
            sx={{
              height: 22,
              bgcolor: alpha(cardColor, 0.12),
              color: cardColor,
              fontWeight: 'bold',
              fontSize: 12,
            }}
          />
        </Stack>

        <Typography variant="body2" sx={{ color: 'text.secondary', flexGrow: 1 }}>
          {description}
        </Typography>
      </Stack>

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        close={handleCloseConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            Delete Category?
          </Typography>
        }
        content={
          <Typography variant="body1">
            Are you sure you want to delete <strong>{name}</strong>?
          </Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'error.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
              isLoading={isDeleting}
            />
            <AppButton
              sx={{ width: '45%' }}
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={handleCloseConfirmDialog}
            />
          </Stack>
        }
      />
    </Card>
  );
}
