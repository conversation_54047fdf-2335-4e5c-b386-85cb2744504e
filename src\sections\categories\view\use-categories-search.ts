import { useState, useEffect, useCallback } from 'react';
import { useCategoriesApi, CategoriesQueryParams } from 'src/services/api/use-categories-api';

// Custom hook for categories search with debounce
export const useCategoriesSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const { useGetCategories } = useCategoriesApi();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
      setIsSearching(false);
    }, 3000); // 300ms debounce

    if (searchQuery !== debouncedQuery) {
      setIsSearching(true);
    }

    return () => clearTimeout(timer);
  }, [searchQuery, debouncedQuery]);

  // Prepare query parameters
  const queryParams: CategoriesQueryParams = {
    take: 15,
    skip: 0,
    ...(debouncedQuery && { name: debouncedQuery }),
  };

  // Get categories with search parameters
  const { data: categoriesResponse, isLoading, isError, refetch } = useGetCategories(queryParams);

  // Handle search input change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  return {
    searchQuery,
    debouncedQuery,
    isSearching,
    categoriesResponse,
    isLoading: isLoading || isSearching,
    isError,
    handleSearchChange,
    clearSearch,
    refetch,
  };
};
