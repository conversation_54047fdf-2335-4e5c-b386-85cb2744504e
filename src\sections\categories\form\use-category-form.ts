import { Category, CreateCategoryRequest, UpdateCategoryRequest } from 'src/services/api/use-categories-api';
import { CategoryFormValues } from './category-schema';

// Helper function to convert color type to backend-compatible theme value (exactly 7 chars)
export const convertColorTypeToTheme = (colorType: string, customColor?: string): string => {
  if (colorType === 'custom') {
    // For custom colors, ensure exactly 7 characters
    const color = customColor || '#FF5733';
    if (color.length === 7) {
      return color; // Perfect length
    }
    if (color.length > 7) {
      return color.substring(0, 7); // Truncate to 7 chars
    }
    return color.padEnd(7, '0'); // Pad to 7 chars
  }

  // Map color type names to backend-compatible codes (exactly 7 characters)
  const colorTypeMap: Record<string, string> = {
    'primary': 'primary',     // 7 chars - OK
    'secondary': 'second1',   // 7 chars - shortened from 'secondary' (9 chars)
    'success': 'success',     // 7 chars - OK
    'warning': 'warning',     // 7 chars - OK
    'info': 'info-cl',        // 7 chars - extended from 'info' (4 chars)
    'error': 'error-c',       // 7 chars - extended from 'error' (5 chars)
  };

  return colorTypeMap[colorType] || colorType.substring(0, 7).padEnd(7, '0');
};

export const convertFormToApiRequest = (
  formData: CategoryFormValues
): CreateCategoryRequest | UpdateCategoryRequest => {
  return {
    name: formData.name,
    description: formData.description,
    icon: formData.icon,
    theme: convertColorTypeToTheme(formData.colorType, formData.customColor),
  };
};

// Helper function to convert backend theme value back to color type
export const convertThemeToColorType = (theme: string): 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error' | 'custom' => {
  if (theme?.startsWith('#')) {
    return 'custom';
  }

  // Map backend codes back to color type names
  const themeToColorTypeMap: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'info' | 'error'> = {
    'primary': 'primary',
    'second1': 'secondary',  // Map 'second1' back to 'secondary'
    'success': 'success',
    'warning': 'warning',
    'info-cl': 'info',       // Map 'info-cl' back to 'info'
    'error-c': 'error',      // Map 'error-c' back to 'error'
  };

  return themeToColorTypeMap[theme] || 'primary';
};

export const convertApiToFormData = (category: Category): CategoryFormValues => {
  // Check if theme is a hex color (custom) or a predefined color type
  const isCustomColor = category.theme?.startsWith('#');

  return {
    name: category.name,
    description: category.description,
    icon: category.icon,
    colorType: convertThemeToColorType(category.theme),
    customColor: isCustomColor ? category.theme : undefined,
  };
};