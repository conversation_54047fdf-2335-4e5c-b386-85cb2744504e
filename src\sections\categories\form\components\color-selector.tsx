import {
  Box,
  Stack,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  IconButton,
  Popover,
} from '@mui/material';
import { useState, useEffect } from 'react';
import { Iconify } from 'src/components/iconify';
import { Controller, Control, useWatch } from 'react-hook-form';
import { CategoryFormValues } from '../category-schema';

interface ColorOption {
  value: string;
  label: string;
  color: string;
}

interface ColorSelectorProps {
  control: Control<CategoryFormValues>;
  error?: boolean;
  helperText?: string;
}

export default function ColorSelector({ control, error, helperText }: ColorSelectorProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [inputValue, setInputValue] = useState('');

  // Watch both colorType and customColor to synchronize them
  const colorType = useWatch({ control, name: 'colorType' });
  const customColor = useWatch({ control, name: 'customColor' });

  const colorOptions: ColorOption[] = [
    { value: 'primary', label: 'Primary', color: theme.palette.primary.main },
    { value: 'secondary', label: 'Secondary', color: theme.palette.secondary.main },
    { value: 'success', label: 'Success', color: theme.palette.success.main },
    { value: 'warning', label: 'Warning', color: theme.palette.warning.main },
    { value: 'info', label: 'Info', color: theme.palette.info.main },
    { value: 'error', label: 'Error', color: theme.palette.error.main },
  ];

  // Helper function to validate hex color
  const isValidHexColor = (color: string): boolean => {
    const hexPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexPattern.test(color);
  };

  // Helper function to get current display color
  const getCurrentColor = (): string => {
    if (colorType === 'custom' && customColor && isValidHexColor(customColor)) {
      return customColor;
    }
    const predefinedOption = colorOptions.find(option => option.value === colorType);
    return predefinedOption?.color || theme.palette.primary.main;
  };

  // Synchronize input value with form state
  useEffect(() => {
    if (colorType === 'custom') {
      setInputValue(customColor || '#FF5733');
    } else {
      const predefinedOption = colorOptions.find(option => option.value === colorType);
      setInputValue(predefinedOption?.color || theme.palette.primary.main);
    }
  }, [colorType, customColor, colorOptions, theme.palette.primary.main]);

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <Stack spacing={1}>
      <Typography variant="body2" sx={{ fontWeight: 500 }}>
        Choose Color
      </Typography>

      {/* Unified Color Input */}
      <Controller
        name="customColor"
        control={control}
        render={({ field: customColorField, fieldState: { error: customColorError } }) => (
          <Controller
            name="colorType"
            control={control}
            render={({ field: colorTypeField }) => (
              <TextField
                fullWidth
                value={inputValue}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setInputValue(newValue);

                  // If it's a valid hex color, set as custom
                  if (isValidHexColor(newValue)) {
                    colorTypeField.onChange('custom');
                    customColorField.onChange(newValue);
                  } else {
                    // Check if it matches a predefined color
                    const matchingOption = colorOptions.find(option =>
                      option.color.toLowerCase() === newValue.toLowerCase() ||
                      option.label.toLowerCase() === newValue.toLowerCase()
                    );
                    if (matchingOption) {
                      colorTypeField.onChange(matchingOption.value);
                      customColorField.onChange('');
                    }
                  }
                }}
                onBlur={() => {
                  // On blur, if the input doesn't match anything valid, revert to current state
                  if (!isValidHexColor(inputValue)) {
                    const matchingOption = colorOptions.find(option =>
                      option.color.toLowerCase() === inputValue.toLowerCase() ||
                      option.label.toLowerCase() === inputValue.toLowerCase()
                    );
                    if (!matchingOption) {
                      // Revert to current valid state
                      if (colorType === 'custom') {
                        setInputValue(customColor || '#FF5733');
                      } else {
                        const currentOption = colorOptions.find(option => option.value === colorType);
                        setInputValue(currentOption?.color || theme.palette.primary.main);
                      }
                    }
                  }
                }}
                placeholder="Enter color (e.g., #FF5733 or Primary)"
                error={!!customColorError || error}
                helperText={customColorError?.message || helperText}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Box
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          bgcolor: getCurrentColor(),
                          border: '1px solid',
                          borderColor: 'divider',
                          mr: 1,
                        }}
                      />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      {/* Color Picker */}
                      <IconButton
                        type="button"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          const input = document.createElement('input');
                          input.type = 'color';
                          input.value = getCurrentColor();
                          input.onchange = (event) => {
                            const target = event.target as HTMLInputElement;
                            const newColor = target.value;
                            setInputValue(newColor);
                            colorTypeField.onChange('custom');
                            customColorField.onChange(newColor);
                          };
                          input.click();
                        }}
                        sx={{ mr: 0.5 }}
                      >
                        <Iconify icon="eva:color-palette-fill" width={20} height={20} />
                      </IconButton>

                      {/* Menu Button */}
                      <IconButton
                        type="button"
                        size="small"
                        onClick={handleOpenMenu}
                      >
                        <Iconify icon="eva:chevron-down-fill" width={20} height={20} />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                }}
              />
            )}
          />
        )}
      />

      {/* Color Menu */}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleCloseMenu}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        slotProps={{
          paper: {
            sx: {
              p: 2,
              minWidth: 300,
              maxWidth: 400,
            },
          },
        }}
      >
        <Stack spacing={2}>
          <Typography variant="subtitle2">Select Color</Typography>

          <Controller
            name="colorType"
            control={control}
            render={({ field: colorTypeField }) => (
              <Controller
                name="customColor"
                control={control}
                render={({ field: customColorField }) => (
                  <Box
                    sx={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(3, 1fr)',
                      gap: 1,
                    }}
                  >
                    {colorOptions.map((option) => (
                      <Box
                        key={option.value}
                        component="button"
                        type="button"
                        onClick={() => {
                          colorTypeField.onChange(option.value);
                          customColorField.onChange('');
                          setInputValue(option.color);
                          handleCloseMenu();
                        }}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          p: 1.5,
                          border: '1px solid',
                          borderColor: colorType === option.value ? 'primary.main' : 'divider',
                          borderRadius: 1,
                          bgcolor: colorType === option.value ? 'primary.lighter' : 'background.paper',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            bgcolor: 'action.hover',
                            borderColor: 'primary.main',
                          },
                        }}
                      >
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: option.color,
                            border: '1px solid',
                            borderColor: 'divider',
                          }}
                        />
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {option.label}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                )}
              />
            )}
          />
        </Stack>
      </Popover>
    </Stack>
  );
}
